<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class createWithdrawAmountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'amount' => 'required|numeric|gt:0',
            // 'paypal_email' => 'email|regex:/(.*)@(.*)\.(.*)/',
        ];
    }
}
